<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_height="wrap_content"
    android:layout_width="match_parent"
    android:layout_margin="8dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="4dp"
    app:cardUseCompatPadding="true">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="200dp"
            android:layout_height="wrap_content"
            android:padding="16dp"
            android:text="Item Title"
            android:textSize="18sp" />
        <EditText
            android:id="@+id/et_rate"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="Enter rate"
            android:inputType="numberDecimal"
            android:padding="16dp" />
    </LinearLayout>

</androidx.cardview.widget.CardView>