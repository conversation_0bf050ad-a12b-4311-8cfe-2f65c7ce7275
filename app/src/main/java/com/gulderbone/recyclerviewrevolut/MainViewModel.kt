package com.gulderbone.recyclerviewrevolut

import androidx.lifecycle.ViewModel
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow

class MainViewModel: ViewModel() {

    private val currencySource = CurrencySource()

    val currencyRatesFlow: Flow<List<CurrencyValue>> = flow {
        while (true) {
            emit(currencySource.getCurrencies())
            delay(1000)
        }
    }

    fun getCurrencyData(): List<CurrencyValue> {
        return currencySource.getCurrencies()
    }

    fun getCurrentRates(): List<CurrencyValue> {
        return currencySource.getCurrencies()
    }
}