package com.gulderbone.recyclerviewrevolut

import android.os.Bundle
import androidx.activity.enableEdgeToEdge
import androidx.appcompat.app.AppCompatActivity
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import kotlinx.coroutines.launch

class MainActivity : AppCompatActivity() {

    private val viewModel = MainViewModel()

    private lateinit var recyclerView: RecyclerView
    private lateinit var adapter: ItemAdapter

    private val items = mutableListOf<Item>()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        setContentView(R.layout.activity_main)
        ViewCompat.setOnApplyWindowInsetsListener(findViewById(R.id.main)) { v, insets ->
            val systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars())
            v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom)
            insets
        }

        recyclerView = findViewById(R.id.recycler_view)

        adapter = ItemAdapter(items)

        recyclerView.layoutManager = LinearLayoutManager(this)
        recyclerView.adapter = adapter

        recyclerView.addItemDecoration(SimpleDividerDecoration(recyclerView, 10, android.R.color.holo_red_dark))

        loadSampleData()

        subscribeToRates()
    }

    private fun loadSampleData() {
        val sampleItems = listOf(
            Item("Item 1", 1.00),
            Item("Item 2", 2.00),
            Item("Item 3", 3.00),
            Item("Item 4", 4.00),
            Item("Item 5", 5.00),
        )

        items.addAll(sampleItems)
        adapter.notifyDataSetChanged()
    }

    private fun subscribeToRates() {
        lifecycleScope.launch {
            viewModel.currencyRatesFlow.collect { currencyRates ->
                items.forEachIndexed { index, item ->
                    val newRate = currencyRates[index].convertedAmount
                    items[index] = item.copy(rate = newRate)
                }
                adapter.notifyDataSetChanged()
            }
        }
    }
}