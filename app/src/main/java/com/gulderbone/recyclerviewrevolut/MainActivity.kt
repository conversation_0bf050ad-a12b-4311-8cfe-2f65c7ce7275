package com.gulderbone.recyclerviewrevolut

import android.os.Bundle
import androidx.activity.enableEdgeToEdge
import androidx.appcompat.app.AppCompatActivity
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import kotlinx.coroutines.launch

class MainActivity : AppCompatActivity() {

    private val viewModel = MainViewModel()

    private lateinit var recyclerView: RecyclerView
    private lateinit var adapter: ItemAdapter

    private val items = mutableListOf<Item>()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        setContentView(R.layout.activity_main)
        ViewCompat.setOnApplyWindowInsetsListener(findViewById(R.id.main)) { v, insets ->
            val systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars())
            v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom)
            insets
        }

        recyclerView = findViewById(R.id.recycler_view)

        adapter = ItemAdapter(items) { baseAmount ->
            updateCurrencyRates(baseAmount)
        }

        recyclerView.layoutManager = LinearLayoutManager(this)
        recyclerView.adapter = adapter

        recyclerView.addItemDecoration(SimpleDividerDecoration(recyclerView, 10, android.R.color.holo_red_dark))

        loadSampleData()

        subscribeToRates()
    }

    private fun loadSampleData() {
        val currencyData = viewModel.getCurrencyData()
        currencyData.forEachIndexed { index, currency ->
            val item = Item(
                code = currency.currencyCode,
                rate = currency.convertedAmount,
                isBaseCurrency = index == 0,
                userInputAmount = if (index == 0) 1.0 else null
            )
            items.add(item)
        }
        adapter.notifyDataSetChanged()
    }

    private fun subscribeToRates() {
        lifecycleScope.launch {
            viewModel.currencyRatesFlow.collect { currencyRates ->
                // Only update rates, don't change the base currency amount
                items.forEachIndexed { index, item ->
                    if (!item.isBaseCurrency) {
                        val baseAmount = items.firstOrNull { it.isBaseCurrency }?.userInputAmount ?: 1.0
                        val baseCurrencyRate = currencyRates.firstOrNull { it.currencyCode == items.first().code }?.convertedAmount ?: 1.0
                        val currentCurrencyRate = currencyRates[index].convertedAmount
                        val convertedAmount = (baseAmount * currentCurrencyRate) / baseCurrencyRate
                        items[index] = item.copy(rate = convertedAmount)
                    }
                }
                adapter.notifyDataSetChanged()
            }
        }
    }

    private fun updateCurrencyRates(baseAmount: Double) {
        val baseCurrencyIndex = items.indexOfFirst { it.isBaseCurrency }
        if (baseCurrencyIndex != -1) {
            items[baseCurrencyIndex] = items[baseCurrencyIndex].copy(userInputAmount = baseAmount)

            // Update all other currencies based on the new base amount
            val currencyRates = viewModel.getCurrentRates()
            val baseCurrencyRate = currencyRates.firstOrNull { it.currencyCode == items[baseCurrencyIndex].code }?.convertedAmount ?: 1.0

            items.forEachIndexed { index, item ->
                if (!item.isBaseCurrency) {
                    val currentCurrencyRate = currencyRates.find { it.currencyCode == item.code }?.convertedAmount ?: 1.0
                    val convertedAmount = (baseAmount * currentCurrencyRate) / baseCurrencyRate
                    items[index] = item.copy(rate = convertedAmount)
                }
            }
            adapter.notifyDataSetChanged()
        }
    }
}