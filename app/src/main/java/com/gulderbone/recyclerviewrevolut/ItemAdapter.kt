package com.gulderbone.recyclerviewrevolut

import android.text.Editable
import android.text.TextWatcher
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.EditText
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView

class ItemAdapter(
    private var items: List<Item>,
    private val onBaseAmountChanged: (Double) -> Unit
) : RecyclerView.Adapter<ItemAdapter.ItemViewHolder>() {

    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int,
    ): ItemViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_layout, parent, false)
        return ItemViewHolder(view)
    }

    override fun onBindViewHolder(holder: ItemViewHolder, position: Int) {
        val item = items[position]
        holder.titleTextView.text = item.code

        // Clear any existing text watcher to avoid conflicts
        holder.rateEditText.tag?.let {
            holder.rateEditText.removeTextChangedListener(it as TextWatcher)
        }

        if (item.isBaseCurrency) {
            // For base currency, show user input amount or default
            val displayAmount = item.userInputAmount ?: 1.0
            holder.rateEditText.setText(String.format("%.2f", displayAmount))
            holder.rateEditText.isEnabled = true

            // Add text watcher for base currency
            val textWatcher = object : TextWatcher {
                override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
                override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
                override fun afterTextChanged(s: Editable?) {
                    val text = s.toString()
                    if (text.isNotEmpty()) {
                        try {
                            val amount = text.toDouble()
                            onBaseAmountChanged(amount)
                        } catch (e: NumberFormatException) {
                            // Handle invalid input
                        }
                    }
                }
            }
            holder.rateEditText.addTextChangedListener(textWatcher)
            holder.rateEditText.tag = textWatcher
        } else {
            // For other currencies, show calculated rate and disable editing
            holder.rateEditText.setText(String.format("%.2f", item.rate))
            holder.rateEditText.isEnabled = false
        }
    }

    override fun getItemCount(): Int = items.size

    class ItemViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val titleTextView: TextView = itemView.findViewById(R.id.tv_title)
        val rateEditText: EditText = itemView.findViewById(R.id.et_rate)
    }
}