package com.gulderbone.recyclerviewrevolut

import android.text.Editable
import android.text.TextWatcher
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.EditText
import android.widget.TextView
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView

class ItemAdapter(
    private var items: List<Item>,
    private val onBaseAmountChanged: (Double) -> Unit
) : ListAdapter<Item, ItemAdapter.ItemViewHolder>(ItemDiffCallback()) {

    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int,
    ): ItemViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_layout, parent, false)
        return ItemViewHolder(view)
    }

    override fun onBindViewHolder(holder: ItemViewHolder, position: Int) {
        val item = items[position]
        holder.titleTextView.text = item.code

        if (item.isBaseCurrency) {
            // For base currency, only set text if it's different to avoid cursor jumping
            val displayAmount = item.userInputAmount ?: 1.0
            val currentText = holder.rateEditText.text.toString()
            val expectedText = String.format("%.2f", displayAmount)

            if (currentText != expectedText && !holder.rateEditText.isFocused) {
                holder.rateEditText.setText(expectedText)
            }

            holder.rateEditText.isEnabled = true

            // Clear any existing text watcher to avoid conflicts
            holder.rateEditText.tag?.let {
                holder.rateEditText.removeTextChangedListener(it as TextWatcher)
            }

            // Add text watcher for base currency
            val textWatcher = object : TextWatcher {
                override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
                override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
                override fun afterTextChanged(s: Editable?) {
                    val text = s.toString()
                    if (text.isNotEmpty()) {
                        try {
                            val amount = text.toDouble()
                            onBaseAmountChanged(amount)
                        } catch (e: NumberFormatException) {
                            // Handle invalid input
                        }
                    }
                }
            }
            holder.rateEditText.addTextChangedListener(textWatcher)
            holder.rateEditText.tag = textWatcher
        } else {
            // Clear any existing text watcher for non-base currencies
            holder.rateEditText.tag?.let {
                holder.rateEditText.removeTextChangedListener(it as TextWatcher)
            }

            // For other currencies, show calculated rate and disable editing
            holder.rateEditText.setText(String.format("%.2f", item.rate))
            holder.rateEditText.isEnabled = false
        }
    }

    override fun getItemCount(): Int = items.size

    class ItemViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val titleTextView: TextView = itemView.findViewById(R.id.tv_title)
        val rateEditText: EditText = itemView.findViewById(R.id.et_rate)
    }
}

class ItemDiffCallback : DiffUtil.ItemCallback<Item>() {
    override fun areItemsTheSame(oldItem: Item, newItem: Item): Boolean {
        return oldItem.code == newItem.code
    }

    override fun areContentsTheSame(oldItem: Item, newItem: Item): Boolean {
        return oldItem == newItem
    }
}