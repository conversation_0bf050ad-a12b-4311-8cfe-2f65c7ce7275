package com.gulderbone.recyclerviewrevolut

import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.Rect
import android.view.View
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView

class SimpleDividerDecoration(
    private val recyclerView: RecyclerView,
    private val dividerHeight: Int = 2,
    private val dividerColor: Int = android.R.color.darker_gray,
) : RecyclerView.ItemDecoration() {

    private val paint = Paint().apply {
        color = ContextCompat.getColor(recyclerView.context, dividerColor)
    }

    override fun getItemOffsets(outRect: Rect, view: View, parent: RecyclerView, state: RecyclerView.State) {
        val position = parent.getChildAdapterPosition(view)
        val itemCount = state.itemCount

        if (position == RecyclerView.NO_POSITION || position == itemCount - 1) {
            return
        }

        if (position < itemCount - 1) {
            outRect.bottom = dividerHeight
        }
    }

    override fun onDraw(c: Can<PERSON>, parent: RecyclerView, state: RecyclerView.State) {
        val left = parent.paddingLeft
        val right = parent.width - parent.paddingRight

        for (i in 0 until parent.childCount) {
            val child = parent.getChildAt(i)
            val position = parent.getChildAdapterPosition(child)

            if (position < state.itemCount - 1) {
                val top = child.bottom
                val bottom = top + dividerHeight

                c.drawRect(left.toFloat(), top.toFloat(), right.toFloat(), bottom.toFloat(), paint)
            }
        }
    }
}