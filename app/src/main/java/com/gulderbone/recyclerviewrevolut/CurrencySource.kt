package com.gulderbone.recyclerviewrevolut

import kotlin.random.Random

internal class CurrencySource {

    private val baseCurrencies = listOf(
        CurrencyValue("USD", 1.0),
        <PERSON><PERSON><PERSON>cyV<PERSON>ue("EUR", 0.85),
        <PERSON><PERSON><PERSON>cy<PERSON><PERSON>ue("GBP", 0.73),
        <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>("JPY", 110.0),
        <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>("CHF", 0.92)
    )

    fun getCurrencies(): List<CurrencyValue> = baseCurrencies.map { currency ->
        CurrencyValue(
            currencyCode = currency.currencyCode,
            convertedAmount = generateRandomAmount(currency.convertedAmount)
        )
    }

    private fun generateRandomAmount(baseRate: Double): Double {
        val fluctuationPercent = Random.nextDouble(-0.05, 0.05)
        val fluctuation = baseRate * fluctuationPercent
        return (baseRate + fluctuation).coerceAtLeast(0.01)
    }
}

data class CurrencyValue(
    var currencyCode: String,
    var convertedAmount: Double
)