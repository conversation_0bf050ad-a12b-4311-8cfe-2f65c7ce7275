package com.gulderbone.recyclerviewrevolut

import kotlin.random.Random

internal class CurrencySource {

    private val baseCurrencies = listOf(
        CurrencyValue("GBP", 1.0),      // Base currency
        CurrencyValue("USD", 1.27),     // 1 GBP = 1.27 USD
        CurrencyValue("EUR", 1.17),     // 1 GBP = 1.17 EUR
        CurrencyValue("JPY", 188.0),    // 1 GBP = 188 JPY
        CurrencyValue("CHF", 1.12)      // 1 GBP = 1.12 CHF
    )

    fun getCurrencies(): List<CurrencyValue> = baseCurrencies.map { currency ->
        CurrencyValue(
            currencyCode = currency.currencyCode,
            convertedAmount = generateRandomAmount(currency.convertedAmount)
        )
    }

    private fun generateRandomAmount(baseRate: Double): Double {
        val fluctuationPercent = Random.nextDouble(-0.05, 0.05)
        val fluctuation = baseRate * fluctuationPercent
        return (baseRate + fluctuation).coerceAtLeast(0.01)
    }
}

data class CurrencyValue(
    var currencyCode: String,
    var convertedAmount: Double
)